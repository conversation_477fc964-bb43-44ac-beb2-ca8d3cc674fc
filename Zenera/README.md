# Zenera E-commerce Platform - Powered by Medoo

Zenera là một hệ thống thương mại điện tử được xây dựng dựa trên nền tảng vững chắc từ **Medoo** - một hệ thống đã được kiểm chứng trong production. Hệ thống tận dụng tối đa các thành phần có giá trị cao từ Medoo để đảm bảo chất lượng enterprise-grade và giảm 69% thời gian phát triển.

## 🎯 Nguyên tắc Thiết kế

1. **Production-tested First** - Tận dụng tối đa các components đã được kiểm chứng từ Medoo
2. **Enterprise-grade Quality** - Sử dụng patterns và utilities đã được validate trong production
3. **Smart Integration** - Kết hợp Medoo (core systems) + Zenera-FE (UI) + zen-buy.be (domain logic)
4. **Scalable Foundation** - Xây dựng trên nền tảng đã được scale trong thực tế

## 🏗️ Kiến trúc Medoo-style

```
zenera/
├── packages/
│   ├── webapp/                      # Main web application (Next.js 15+)
│   │   ├── app/
│   │   │   ├── [locale]/           # i18n support (Medoo pattern)
│   │   │   │   ├── (buyer)/        # Buyer pages
│   │   │   │   ├── (seller)/       # Seller pages
│   │   │   │   └── (admin)/        # Admin pages
│   │   │   └── api/                # API routes
│   │   ├── components/             # Component library (Medoo pattern)
│   │   │   ├── shared/             # Shared components
│   │   │   ├── buyer/              # Buyer-specific components
│   │   │   ├── seller/             # Seller-specific components
│   │   │   └── admin/              # Admin-specific components
│   │   ├── schema-form/            # Schema-form system (từ Medoo)
│   │   ├── lib/                    # Utilities & hooks (Medoo pattern)
│   │   ├── store/                  # State management
│   │   └── styles/                 # Styling system
│   ├── api-server/                 # Backend API (NestJS)
│   ├── sharing/                    # Shared libraries (Medoo pattern)
│   └── ui-components/              # Design System package
├── tools/                          # Development tools
├── lerna.json                      # Lerna configuration (Medoo pattern)
└── pnpm-workspace.yaml             # PNPM workspace configuration
```

## 🛠️ Technology Stack - Production-tested

### Frontend (Medoo + Zenera-FE Integration)
- **Next.js 15+** với App Router
- **Medoo Schema-Form System** - Dynamic form generation đã được kiểm chứng
- **Shadcn/ui** (từ Zenera-FE) - Modern, accessible components
- **Medoo i18n System** - Multi-language support với SSR
- **Tailwind CSS** cho styling
- **Zustand + Medoo Auth Patterns** - Production-tested auth flows
- **Medoo HTTP Client + TanStack Query** - Enterprise-grade API layer

### Backend (zen-buy.be + Medoo Patterns)
- **NestJS** framework (từ zen-buy.be)
- **MongoDB** với Mongoose
- **JWT + Refresh tokens** (Medoo patterns)
- **Class-validator + Zod**
- **Swagger** documentation

### Shared Libraries (Medoo Utilities)
- **Medoo Common Utilities** - Production-tested utility functions
- **Medoo Validation System** - Comprehensive validation
- **TypeScript** strict mode
- **Zod schemas** + Medoo validation patterns

### Development
- **Turborepo** monorepo management
- **pnpm** package manager
- **ESLint** + **Prettier**
- **Vitest** + **Testing Library**

## 📋 Tính năng theo Role

### 👥 Buyer Features
- Product browsing & search
- Shopping cart & checkout
- Order tracking
- User profile management

### 🏪 Seller Features
- Product management
- Order processing
- Sales analytics
- Inventory tracking

### 👨‍💼 Admin Features
- User management
- Product moderation
- System settings
- Platform analytics

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- MongoDB
- pnpm

### Installation
```bash
# Clone repository
git clone https://github.com/zenera/zenera.git
cd zenera

# Install dependencies
pnpm install

# Setup environment
cp .env.example .env.local

# Start development servers
pnpm dev
```

### Development Commands
```bash
# Start all packages in development mode
pnpm dev

# Build all packages
pnpm build

# Run tests
pnpm test

# Lint code
pnpm lint
```

## 📚 Documentation

### **Core Architecture**
- [Zenera Architecture with Medoo](./docs/zenera-architecture-with-medoo.md) - Complete system architecture
- [Design System Architecture](./docs/design-system-architecture.md) - Custom Design System với animations

### **Implementation Guides**
- [Integration Analysis & Plan](./docs/integration-analysis-and-plan.md) - Conflict analysis và recommended approach
- [Detailed Project Plan](./docs/detailed-project-plan.md) - 5-week step-by-step implementation plan

### **Reference Materials**
- [Medoo Components Extract](./docs/medoo-extract.md) - Medoo components reference guide

## 🚀 Migration Strategy - Selective Integration (Recommended)

**Approach**: Cherry-pick valuable, low-conflict components from Medoo
**Timeline**: 5 weeks instead of 8 weeks
**Risk**: Medium (4/10) instead of High (7/10)

### Week 1: Foundation
- [x] Setup Medoo-style monorepo structure với pnpm workspaces
- [x] Create architecture documentation
- [ ] Setup Next.js 15.x app trong packages/webapp
- [ ] Copy zen-buy.be backend structure vào packages/api-server
- [ ] Basic frontend-backend integration

### Week 2: Core Utilities
- [ ] Extract Medoo auth utilities (not full system)
- [ ] Extract Medoo common utilities
- [ ] Create simple Zustand stores
- [ ] Setup basic API client

### Week 3: Forms & i18n
- [ ] Build forms với React Hook Form + Zod
- [ ] Extract Medoo i18n hooks (adapted)
- [ ] Create e-commerce form schemas
- [ ] Setup translation system

### Week 4: E-commerce Features
- [ ] Build buyer features
- [ ] Build seller features
- [ ] Build admin features
- [ ] Integration testing

### Week 5: Polish & Deploy
- [ ] Performance optimization
- [ ] Bug fixes và documentation
- [ ] Production deployment

## 🎯 Key Benefits

### Production-tested Quality
- **Enterprise-grade**: Components đã được validate trong production
- **Bug-free**: Bugs đã được fix qua thời gian sử dụng thực tế
- **Performance**: Đã được optimize cho production workload
- **Accessibility**: Hỗ trợ accessibility standards

### Massive Time Savings
- **Authentication System**: 4 weeks → 1 week (75% savings)
- **Schema-form System**: 5 weeks → 1.5 weeks (70% savings)
- **i18n System**: 2 weeks → 3 days (85% savings)
- **API Layer**: 3 weeks → 1 week (67% savings)
- **Total**: 18 weeks → 5.5 weeks (69% time reduction)

### Custom Design System
- **Unique Visual Identity**: Custom animations và styling riêng cho Zenera
- **Scalable Architecture**: Dễ dàng update/thay đổi style cho toàn hệ thống
- **Performance**: Optimized animations không ảnh hưởng performance
- **Consistency**: Đảm bảo UI/UX nhất quán across all features

### Risk Mitigation
- **Proven Architecture**: Patterns đã được validate trong production
- **Team Knowledge**: Team đã familiar với Medoo codebase
- **Maintenance**: Easier maintenance với familiar code patterns

## 📊 Project Status

| Component | Source | Status | Priority | Value |
|-----------|--------|--------|----------|-------|
| **Auth System** | Medoo | 🟢 Production-tested | Critical | ⭐⭐⭐⭐⭐ |
| **Schema-Form System** | Medoo | 🟢 Production-tested | Critical | ⭐⭐⭐⭐⭐ |
| **i18n System** | Medoo | 🟢 Production-tested | Critical | ⭐⭐⭐⭐⭐ |
| **API Layer** | Medoo | 🟢 Production-tested | Critical | ⭐⭐⭐⭐⭐ |
| **Common Utilities** | Medoo | 🟢 Production-tested | Critical | ⭐⭐⭐⭐⭐ |
| **UI Components** | Zenera-FE | 🟡 Ready | High | ⭐⭐⭐⭐ |
| **Backend API** | zen-buy.be | 🟡 Ready | High | ⭐⭐⭐⭐ |
| **Layout Components** | Medoo | 🟡 Ready | Medium | ⭐⭐⭐ |

## 📞 Support

- **Documentation**: [./docs/](./docs/)
- **Architecture with Medoo**: [./docs/zenera-architecture-with-medoo.md](./docs/zenera-architecture-with-medoo.md)
- **Medoo Components Analysis**: [./docs/medoo-extract.md](./docs/medoo-extract.md)
- **Reusable Components**: [./docs/reusable-components-analysis.md](./docs/reusable-components-analysis.md)

## 📄 License

This project is licensed under the MIT License.

---

**Built on the solid foundation of Medoo - Production-tested, Enterprise-grade 🚀**
