{"lastValidatedTimestamp": 1748850459092, "projects": {"/Volumes/Work/DuAnNgoai/Zenera/Zenera": {"name": "zenera", "version": "1.0.0"}, "/Volumes/Work/DuAnNgoai/Zenera/Zenera/packages/api-server": {"name": "@zenera/api-server", "version": "1.0.0"}, "/Volumes/Work/DuAnNgoai/Zenera/Zenera/packages/sharing": {"name": "@zenera/sharing", "version": "1.0.0"}, "/Volumes/Work/DuAnNgoai/Zenera/Zenera/packages/ui-components": {"name": "@zenera/ui-components", "version": "1.0.0"}, "/Volumes/Work/DuAnNgoai/Zenera/Zenera/packages/webapp": {"name": "@zenera/webapp", "version": "0.1.0"}}, "pnpmfileExists": false, "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["packages/*"]}, "filteredInstall": false}