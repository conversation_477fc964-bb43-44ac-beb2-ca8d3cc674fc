#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/jest-cli@29.7.0_@types+node@20.17.57_ts-node@10.9.2_@types+node@20.17.57_typescript@5.8.3_/node_modules/jest-cli/bin/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/jest-cli@29.7.0_@types+node@20.17.57_ts-node@10.9.2_@types+node@20.17.57_typescript@5.8.3_/node_modules/jest-cli/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/jest-cli@29.7.0_@types+node@20.17.57_ts-node@10.9.2_@types+node@20.17.57_typescript@5.8.3_/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/jest-cli@29.7.0_@types+node@20.17.57_ts-node@10.9.2_@types+node@20.17.57_typescript@5.8.3_/node_modules/jest-cli/bin/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/jest-cli@29.7.0_@types+node@20.17.57_ts-node@10.9.2_@types+node@20.17.57_typescript@5.8.3_/node_modules/jest-cli/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/jest-cli@29.7.0_@types+node@20.17.57_ts-node@10.9.2_@types+node@20.17.57_typescript@5.8.3_/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/jest.js" "$@"
else
  exec node  "$basedir/../../bin/jest.js" "$@"
fi
