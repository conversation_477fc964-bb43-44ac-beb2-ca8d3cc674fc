#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/@nuxtjs+opencollective@0.3.2/node_modules/@nuxtjs/opencollective/bin/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/@nuxtjs+opencollective@0.3.2/node_modules/@nuxtjs/opencollective/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/@nuxtjs+opencollective@0.3.2/node_modules/@nuxtjs/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/@nuxtjs+opencollective@0.3.2/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/@nuxtjs+opencollective@0.3.2/node_modules/@nuxtjs/opencollective/bin/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/@nuxtjs+opencollective@0.3.2/node_modules/@nuxtjs/opencollective/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/@nuxtjs+opencollective@0.3.2/node_modules/@nuxtjs/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/@nuxtjs+opencollective@0.3.2/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/opencollective.js" "$@"
else
  exec node  "$basedir/../../bin/opencollective.js" "$@"
fi
