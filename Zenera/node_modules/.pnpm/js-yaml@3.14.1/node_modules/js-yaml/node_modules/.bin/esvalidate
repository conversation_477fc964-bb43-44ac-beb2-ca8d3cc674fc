#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/esprima@4.0.1/node_modules/esprima/bin/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/esprima@4.0.1/node_modules/esprima/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/esprima@4.0.1/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/esprima@4.0.1/node_modules/esprima/bin/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/esprima@4.0.1/node_modules/esprima/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/esprima@4.0.1/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../esprima@4.0.1/node_modules/esprima/bin/esvalidate.js" "$@"
else
  exec node  "$basedir/../../../../../esprima@4.0.1/node_modules/esprima/bin/esvalidate.js" "$@"
fi
