#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0/node_modules/@angular-devkit/schematics-cli/bin/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0/node_modules/@angular-devkit/schematics-cli/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0/node_modules/@angular-devkit/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0/node_modules/@angular-devkit/schematics-cli/bin/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0/node_modules/@angular-devkit/schematics-cli/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0/node_modules/@angular-devkit/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0/node_modules/@angular-devkit/schematics-cli/bin/schematics.js" "$@"
else
  exec node  "$basedir/../../../../../../@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0/node_modules/@angular-devkit/schematics-cli/bin/schematics.js" "$@"
fi
