#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/ts-jest@29.3.4_@babel+core@7.27.4_@jest+transform@29.7.0_@jest+types@29.6.3_babel-jest@_6748ad135ca5e78561c2870afa9596b1/node_modules/ts-jest/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/ts-jest@29.3.4_@babel+core@7.27.4_@jest+transform@29.7.0_@jest+types@29.6.3_babel-jest@_6748ad135ca5e78561c2870afa9596b1/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/ts-jest@29.3.4_@babel+core@7.27.4_@jest+transform@29.7.0_@jest+types@29.6.3_babel-jest@_6748ad135ca5e78561c2870afa9596b1/node_modules/ts-jest/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/ts-jest@29.3.4_@babel+core@7.27.4_@jest+transform@29.7.0_@jest+types@29.6.3_babel-jest@_6748ad135ca5e78561c2870afa9596b1/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ts-jest/cli.js" "$@"
else
  exec node  "$basedir/../ts-jest/cli.js" "$@"
fi
