#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.17.57_typescript@5.8.3/node_modules/ts-node/dist/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.17.57_typescript@5.8.3/node_modules/ts-node/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.17.57_typescript@5.8.3/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.17.57_typescript@5.8.3/node_modules/ts-node/dist/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.17.57_typescript@5.8.3/node_modules/ts-node/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/ts-node@10.9.2_@types+node@20.17.57_typescript@5.8.3/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../dist/bin-transpile.js" "$@"
else
  exec node  "$basedir/../../dist/bin-transpile.js" "$@"
fi
