#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/vite@4.5.14_@types+node@20.17.57_lightningcss@1.30.1_terser@5.40.0/node_modules/vite/bin/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/vite@4.5.14_@types+node@20.17.57_lightningcss@1.30.1_terser@5.40.0/node_modules/vite/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/vite@4.5.14_@types+node@20.17.57_lightningcss@1.30.1_terser@5.40.0/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/vite@4.5.14_@types+node@20.17.57_lightningcss@1.30.1_terser@5.40.0/node_modules/vite/bin/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/vite@4.5.14_@types+node@20.17.57_lightningcss@1.30.1_terser@5.40.0/node_modules/vite/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/vite@4.5.14_@types+node@20.17.57_lightningcss@1.30.1_terser@5.40.0/node_modules:/Volumes/Work/DuAnNgoai/Zenera/Zenera/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../vite@4.5.14_@types+node@20.17.57_lightningcss@1.30.1_terser@5.40.0/node_modules/vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../../../../../../vite@4.5.14_@types+node@20.17.57_lightningcss@1.30.1_terser@5.40.0/node_modules/vite/bin/vite.js" "$@"
fi
