// Zenera Typography System
export const typography = {
  // Font Families
  fontFamily: {
    sans: ['Inter', 'system-ui', 'sans-serif'],
    serif: ['Georgia', 'serif'],
    mono: ['JetBrains Mono', 'Consolas', 'monospace'],
    display: ['Poppins', 'Inter', 'system-ui', 'sans-serif'], // For headings
  },

  // Font Sizes (rem based)
  fontSize: {
    xs: ['0.75rem', { lineHeight: '1rem' }],      // 12px
    sm: ['0.875rem', { lineHeight: '1.25rem' }],  // 14px
    base: ['1rem', { lineHeight: '1.5rem' }],     // 16px
    lg: ['1.125rem', { lineHeight: '1.75rem' }],  // 18px
    xl: ['1.25rem', { lineHeight: '1.75rem' }],   // 20px
    '2xl': ['1.5rem', { lineHeight: '2rem' }],    // 24px
    '3xl': ['1.875rem', { lineHeight: '2.25rem' }], // 30px
    '4xl': ['2.25rem', { lineHeight: '2.5rem' }], // 36px
    '5xl': ['3rem', { lineHeight: '1' }],         // 48px
    '6xl': ['3.75rem', { lineHeight: '1' }],      // 60px
    '7xl': ['4.5rem', { lineHeight: '1' }],       // 72px
    '8xl': ['6rem', { lineHeight: '1' }],         // 96px
    '9xl': ['8rem', { lineHeight: '1' }],         // 128px
  },

  // Font Weights
  fontWeight: {
    thin: '100',
    extralight: '200',
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
    black: '900',
  },

  // Line Heights
  lineHeight: {
    none: '1',
    tight: '1.25',
    snug: '1.375',
    normal: '1.5',
    relaxed: '1.625',
    loose: '2',
  },

  // Letter Spacing
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0em',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em',
  },

  // Text Styles for E-commerce
  textStyles: {
    // Headings
    'heading-1': {
      fontFamily: 'Poppins',
      fontSize: '3rem',
      fontWeight: '700',
      lineHeight: '1.2',
      letterSpacing: '-0.025em',
    },
    'heading-2': {
      fontFamily: 'Poppins',
      fontSize: '2.25rem',
      fontWeight: '600',
      lineHeight: '1.3',
      letterSpacing: '-0.025em',
    },
    'heading-3': {
      fontFamily: 'Poppins',
      fontSize: '1.875rem',
      fontWeight: '600',
      lineHeight: '1.4',
    },
    'heading-4': {
      fontFamily: 'Poppins',
      fontSize: '1.5rem',
      fontWeight: '600',
      lineHeight: '1.4',
    },
    'heading-5': {
      fontFamily: 'Poppins',
      fontSize: '1.25rem',
      fontWeight: '600',
      lineHeight: '1.5',
    },
    'heading-6': {
      fontFamily: 'Poppins',
      fontSize: '1.125rem',
      fontWeight: '600',
      lineHeight: '1.5',
    },

    // Body Text
    'body-large': {
      fontFamily: 'Inter',
      fontSize: '1.125rem',
      fontWeight: '400',
      lineHeight: '1.7',
    },
    'body-base': {
      fontFamily: 'Inter',
      fontSize: '1rem',
      fontWeight: '400',
      lineHeight: '1.6',
    },
    'body-small': {
      fontFamily: 'Inter',
      fontSize: '0.875rem',
      fontWeight: '400',
      lineHeight: '1.5',
    },

    // E-commerce Specific
    'product-title': {
      fontFamily: 'Poppins',
      fontSize: '1.5rem',
      fontWeight: '600',
      lineHeight: '1.4',
    },
    'product-price': {
      fontFamily: 'Poppins',
      fontSize: '1.25rem',
      fontWeight: '700',
      lineHeight: '1.2',
    },
    'product-description': {
      fontFamily: 'Inter',
      fontSize: '0.875rem',
      fontWeight: '400',
      lineHeight: '1.6',
    },
    'button-text': {
      fontFamily: 'Inter',
      fontSize: '0.875rem',
      fontWeight: '600',
      lineHeight: '1.2',
      letterSpacing: '0.025em',
    },
    'label': {
      fontFamily: 'Inter',
      fontSize: '0.875rem',
      fontWeight: '500',
      lineHeight: '1.4',
    },
    'caption': {
      fontFamily: 'Inter',
      fontSize: '0.75rem',
      fontWeight: '400',
      lineHeight: '1.4',
    },
  },
} as const;

export type FontFamily = keyof typeof typography.fontFamily;
export type FontSize = keyof typeof typography.fontSize;
export type FontWeight = keyof typeof typography.fontWeight;
export type TextStyle = keyof typeof typography.textStyles;
