// Zenera Design Tokens - Complete Export
export * from './colors';
export * from './typography';
export * from './spacing';
export * from './animations';

// Re-export for convenience
export { colors } from './colors';
export { typography } from './typography';
export { spacing, componentSpacing, borderRadius, shadows } from './spacing';
export { animations } from './animations';

// Design token theme object
export const tokens = {
  colors: require('./colors').colors,
  typography: require('./typography').typography,
  spacing: require('./spacing').spacing,
  componentSpacing: require('./spacing').componentSpacing,
  borderRadius: require('./spacing').borderRadius,
  shadows: require('./spacing').shadows,
  animations: require('./animations').animations,
} as const;
