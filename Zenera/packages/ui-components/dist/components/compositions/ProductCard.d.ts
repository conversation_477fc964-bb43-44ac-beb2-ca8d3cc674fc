import * as React from 'react';
import { type VariantProps } from 'class-variance-authority';
declare const productCardVariants: (props?: ({
    variant?: "default" | "compact" | "featured" | "minimal" | null | undefined;
    size?: "default" | "sm" | "lg" | "xl" | null | undefined;
} & import("class-variance-authority/types").ClassProp) | undefined) => string;
export interface ProductCardProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof productCardVariants> {
    product: {
        id: string;
        name: string;
        price: number;
        originalPrice?: number;
        image: string;
        rating?: number;
        reviewCount?: number;
        badge?: string;
        inStock?: boolean;
    };
    onAddToCart?: (productId: string) => void;
    onAddToWishlist?: (productId: string) => void;
    onQuickView?: (productId: string) => void;
    showQuickActions?: boolean;
    loading?: boolean;
}
declare const ProductCard: React.ForwardRefExoticComponent<ProductCardProps & React.RefAttributes<HTMLDivElement>>;
export { ProductCard, productCardVariants };
//# sourceMappingURL=ProductCard.d.ts.map