// Zenera Spacing System
export const spacing = {
    // Base spacing scale (rem based)
    0: '0',
    px: '1px',
    0.5: '0.125rem', // 2px
    1: '0.25rem', // 4px
    1.5: '0.375rem', // 6px
    2: '0.5rem', // 8px
    2.5: '0.625rem', // 10px
    3: '0.75rem', // 12px
    3.5: '0.875rem', // 14px
    4: '1rem', // 16px
    5: '1.25rem', // 20px
    6: '1.5rem', // 24px
    7: '1.75rem', // 28px
    8: '2rem', // 32px
    9: '2.25rem', // 36px
    10: '2.5rem', // 40px
    11: '2.75rem', // 44px
    12: '3rem', // 48px
    14: '3.5rem', // 56px
    16: '4rem', // 64px
    20: '5rem', // 80px
    24: '6rem', // 96px
    28: '7rem', // 112px
    32: '8rem', // 128px
    36: '9rem', // 144px
    40: '10rem', // 160px
    44: '11rem', // 176px
    48: '12rem', // 192px
    52: '13rem', // 208px
    56: '14rem', // 224px
    60: '15rem', // 240px
    64: '16rem', // 256px
    72: '18rem', // 288px
    80: '20rem', // 320px
    96: '24rem', // 384px
};
// Component-specific spacing
export const componentSpacing = {
    // Button spacing
    button: {
        padding: {
            sm: { x: spacing[3], y: spacing[1.5] },
            md: { x: spacing[4], y: spacing[2] },
            lg: { x: spacing[6], y: spacing[3] },
            xl: { x: spacing[8], y: spacing[4] },
        },
        gap: spacing[2],
    },
    // Input spacing
    input: {
        padding: {
            sm: { x: spacing[3], y: spacing[1.5] },
            md: { x: spacing[4], y: spacing[2.5] },
            lg: { x: spacing[4], y: spacing[3] },
        },
    },
    // Card spacing
    card: {
        padding: {
            sm: spacing[4],
            md: spacing[6],
            lg: spacing[8],
        },
        gap: spacing[4],
    },
    // Layout spacing
    layout: {
        container: {
            padding: { x: spacing[4], y: spacing[8] },
            maxWidth: '1200px',
        },
        section: {
            padding: { y: spacing[16] },
            gap: spacing[12],
        },
        grid: {
            gap: spacing[6],
        },
    },
    // E-commerce specific spacing
    product: {
        card: {
            padding: spacing[4],
            gap: spacing[3],
            imageAspect: '1/1',
        },
        gallery: {
            gap: spacing[2],
            thumbnailSize: spacing[16],
        },
        details: {
            gap: spacing[6],
            sectionGap: spacing[8],
        },
    },
    // Navigation spacing
    navigation: {
        header: {
            height: spacing[16],
            padding: { x: spacing[6] },
        },
        sidebar: {
            width: spacing[64],
            padding: spacing[6],
        },
        breadcrumb: {
            gap: spacing[2],
            padding: { y: spacing[4] },
        },
    },
    // Form spacing
    form: {
        fieldGap: spacing[4],
        sectionGap: spacing[8],
        labelGap: spacing[1.5],
        buttonGap: spacing[4],
    },
};
// Border radius scale
export const borderRadius = {
    none: '0',
    sm: '0.125rem', // 2px
    base: '0.25rem', // 4px
    md: '0.375rem', // 6px
    lg: '0.5rem', // 8px
    xl: '0.75rem', // 12px
    '2xl': '1rem', // 16px
    '3xl': '1.5rem', // 24px
    full: '9999px',
};
// Shadow scale
export const shadows = {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    base: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
    inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
    none: '0 0 #0000',
};
