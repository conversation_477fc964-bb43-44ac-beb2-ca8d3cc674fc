// Zenera Animation System - E-commerce Optimized
export const animations = {
    // Duration scale
    duration: {
        instant: '0ms',
        fast: '150ms',
        normal: '300ms',
        slow: '500ms',
        slower: '750ms',
        slowest: '1000ms',
    },
    // Easing functions
    easing: {
        linear: 'linear',
        ease: 'ease',
        easeIn: 'ease-in',
        easeOut: 'ease-out',
        easeInOut: 'ease-in-out',
        // Custom easing for smooth animations
        smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',
        bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
        elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
    },
    // Keyframes
    keyframes: {
        fadeIn: {
            from: { opacity: '0' },
            to: { opacity: '1' },
        },
        fadeOut: {
            from: { opacity: '1' },
            to: { opacity: '0' },
        },
        slideInUp: {
            from: { transform: 'translateY(100%)', opacity: '0' },
            to: { transform: 'translateY(0)', opacity: '1' },
        },
        slideInDown: {
            from: { transform: 'translateY(-100%)', opacity: '0' },
            to: { transform: 'translateY(0)', opacity: '1' },
        },
        slideInLeft: {
            from: { transform: 'translateX(-100%)', opacity: '0' },
            to: { transform: 'translateX(0)', opacity: '1' },
        },
        slideInRight: {
            from: { transform: 'translateX(100%)', opacity: '0' },
            to: { transform: 'translateX(0)', opacity: '1' },
        },
        scaleIn: {
            from: { transform: 'scale(0.9)', opacity: '0' },
            to: { transform: 'scale(1)', opacity: '1' },
        },
        scaleOut: {
            from: { transform: 'scale(1)', opacity: '1' },
            to: { transform: 'scale(0.9)', opacity: '0' },
        },
        bounce: {
            '0%, 20%, 53%, 80%, 100%': { transform: 'translate3d(0,0,0)' },
            '40%, 43%': { transform: 'translate3d(0, -30px, 0)' },
            '70%': { transform: 'translate3d(0, -15px, 0)' },
            '90%': { transform: 'translate3d(0, -4px, 0)' },
        },
        pulse: {
            '0%, 100%': { opacity: '1' },
            '50%': { opacity: '0.5' },
        },
        spin: {
            from: { transform: 'rotate(0deg)' },
            to: { transform: 'rotate(360deg)' },
        },
        wiggle: {
            '0%, 100%': { transform: 'rotate(-3deg)' },
            '50%': { transform: 'rotate(3deg)' },
        },
        // E-commerce specific animations
        addToCart: {
            '0%': { transform: 'scale(1)' },
            '50%': { transform: 'scale(1.1)' },
            '100%': { transform: 'scale(1)' },
        },
        priceChange: {
            '0%': { transform: 'scale(1)', color: 'inherit' },
            '50%': { transform: 'scale(1.05)', color: '#22c55e' },
            '100%': { transform: 'scale(1)', color: 'inherit' },
        },
        stockAlert: {
            '0%, 100%': { backgroundColor: 'transparent' },
            '50%': { backgroundColor: '#fef3c7' },
        },
    },
    // Pre-defined animation classes
    presets: {
        // Entrance animations
        fadeIn: {
            animation: 'fadeIn 300ms cubic-bezier(0.4, 0, 0.2, 1)',
        },
        slideInUp: {
            animation: 'slideInUp 300ms cubic-bezier(0.4, 0, 0.2, 1)',
        },
        slideInDown: {
            animation: 'slideInDown 300ms cubic-bezier(0.4, 0, 0.2, 1)',
        },
        scaleIn: {
            animation: 'scaleIn 200ms cubic-bezier(0.4, 0, 0.2, 1)',
        },
        // Hover effects
        hoverLift: {
            transition: 'transform 150ms cubic-bezier(0.4, 0, 0.2, 1)',
            '&:hover': {
                transform: 'translateY(-2px)',
            },
        },
        hoverScale: {
            transition: 'transform 150ms cubic-bezier(0.4, 0, 0.2, 1)',
            '&:hover': {
                transform: 'scale(1.02)',
            },
        },
        hoverGlow: {
            transition: 'box-shadow 150ms cubic-bezier(0.4, 0, 0.2, 1)',
            '&:hover': {
                boxShadow: '0 10px 25px -5px rgba(59, 130, 246, 0.15)',
            },
        },
        // Loading states
        pulse: {
            animation: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        },
        spin: {
            animation: 'spin 1s linear infinite',
        },
        // E-commerce interactions
        addToCart: {
            animation: 'addToCart 200ms cubic-bezier(0.68, -0.55, 0.265, 1.55)',
        },
        priceChange: {
            animation: 'priceChange 500ms cubic-bezier(0.4, 0, 0.2, 1)',
        },
        stockAlert: {
            animation: 'stockAlert 1s ease-in-out infinite',
        },
        // Button states
        buttonPress: {
            transition: 'transform 100ms cubic-bezier(0.4, 0, 0.2, 1)',
            '&:active': {
                transform: 'scale(0.98)',
            },
        },
        // Page transitions
        pageEnter: {
            animation: 'fadeIn 400ms cubic-bezier(0.4, 0, 0.2, 1)',
        },
        pageExit: {
            animation: 'fadeOut 200ms cubic-bezier(0.4, 0, 0.2, 1)',
        },
    },
    // Micro-interactions
    microInteractions: {
        // Form interactions
        inputFocus: {
            transition: 'all 150ms cubic-bezier(0.4, 0, 0.2, 1)',
            '&:focus': {
                transform: 'scale(1.01)',
                boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)',
            },
        },
        // Card interactions
        cardHover: {
            transition: 'all 200ms cubic-bezier(0.4, 0, 0.2, 1)',
            '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
            },
        },
        // Navigation interactions
        navItemHover: {
            transition: 'color 150ms cubic-bezier(0.4, 0, 0.2, 1)',
            position: 'relative',
            '&::after': {
                content: '""',
                position: 'absolute',
                bottom: '-2px',
                left: '0',
                width: '0',
                height: '2px',
                backgroundColor: 'currentColor',
                transition: 'width 200ms cubic-bezier(0.4, 0, 0.2, 1)',
            },
            '&:hover::after': {
                width: '100%',
            },
        },
    },
};
