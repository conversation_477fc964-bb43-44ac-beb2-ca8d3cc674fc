import { z } from 'zod';
export declare const userSchema: z.ZodObject<{
    email: z.ZodString;
    name: z.ZodString;
    role: z.<PERSON><PERSON><["buyer", "seller", "admin"]>;
}, "strip", z.ZodTypeAny, {
    email: string;
    name: string;
    role: "buyer" | "seller" | "admin";
}, {
    email: string;
    name: string;
    role: "buyer" | "seller" | "admin";
}>;
export declare const loginSchema: z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
}, "strip", z.ZodTypeAny, {
    email: string;
    password: string;
}, {
    email: string;
    password: string;
}>;
export declare const registerSchema: z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
    name: z.ZodString;
    role: z.ZodDefault<z.ZodEnum<["buyer", "seller"]>>;
}, "strip", z.ZodType<PERSON>ny, {
    email: string;
    name: string;
    role: "buyer" | "seller";
    password: string;
}, {
    email: string;
    name: string;
    password: string;
    role?: "buyer" | "seller" | undefined;
}>;
export declare const productSchema: z.ZodObject<{
    name: z.ZodString;
    description: z.ZodString;
    price: z.ZodNumber;
    originalPrice: z.ZodOptional<z.ZodNumber>;
    category: z.ZodString;
    tags: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
    stockQuantity: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    name: string;
    description: string;
    price: number;
    category: string;
    tags: string[];
    stockQuantity: number;
    originalPrice?: number | undefined;
}, {
    name: string;
    description: string;
    price: number;
    category: string;
    stockQuantity: number;
    originalPrice?: number | undefined;
    tags?: string[] | undefined;
}>;
export declare const orderSchema: z.ZodObject<{
    items: z.ZodArray<z.ZodObject<{
        productId: z.ZodString;
        quantity: z.ZodNumber;
        price: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        price: number;
        productId: string;
        quantity: number;
    }, {
        price: number;
        productId: string;
        quantity: number;
    }>, "many">;
    shippingAddress: z.ZodObject<{
        street: z.ZodString;
        city: z.ZodString;
        state: z.ZodString;
        zipCode: z.ZodString;
        country: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        street: string;
        city: string;
        state: string;
        zipCode: string;
        country: string;
    }, {
        street: string;
        city: string;
        state: string;
        zipCode: string;
        country: string;
    }>;
    paymentMethod: z.ZodString;
}, "strip", z.ZodTypeAny, {
    items: {
        price: number;
        productId: string;
        quantity: number;
    }[];
    shippingAddress: {
        street: string;
        city: string;
        state: string;
        zipCode: string;
        country: string;
    };
    paymentMethod: string;
}, {
    items: {
        price: number;
        productId: string;
        quantity: number;
    }[];
    shippingAddress: {
        street: string;
        city: string;
        state: string;
        zipCode: string;
        country: string;
    };
    paymentMethod: string;
}>;
export type UserInput = z.infer<typeof userSchema>;
export type LoginInput = z.infer<typeof loginSchema>;
export type RegisterInput = z.infer<typeof registerSchema>;
export type ProductInput = z.infer<typeof productSchema>;
export type OrderInput = z.infer<typeof orderSchema>;
//# sourceMappingURL=index.d.ts.map