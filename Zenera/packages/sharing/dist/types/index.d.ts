export interface User {
    id: string;
    email: string;
    name: string;
    role: 'buyer' | 'seller' | 'admin';
    avatar?: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface Product {
    id: string;
    name: string;
    description: string;
    price: number;
    originalPrice?: number;
    images: string[];
    category: string;
    tags: string[];
    sellerId: string;
    inStock: boolean;
    stockQuantity: number;
    rating: number;
    reviewCount: number;
    createdAt: Date;
    updatedAt: Date;
}
export interface Order {
    id: string;
    buyerId: string;
    items: OrderItem[];
    total: number;
    status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
    shippingAddress: Address;
    paymentMethod: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface OrderItem {
    productId: string;
    quantity: number;
    price: number;
}
export interface Address {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
}
//# sourceMappingURL=index.d.ts.map