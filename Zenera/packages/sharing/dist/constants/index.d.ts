export declare const USER_ROLES: {
    readonly BUYER: "buyer";
    readonly SELLER: "seller";
    readonly ADMIN: "admin";
};
export declare const ORDER_STATUS: {
    readonly PENDING: "pending";
    readonly PROCESSING: "processing";
    readonly SHIPPED: "shipped";
    readonly DELIVERED: "delivered";
    readonly CANCELLED: "cancelled";
};
export declare const PRODUCT_CATEGORIES: readonly ["Electronics", "Clothing", "Home & Garden", "Sports & Outdoors", "Books", "Health & Beauty", "Toys & Games", "Automotive"];
export declare const API_ENDPOINTS: {
    readonly AUTH: {
        readonly LOGIN: "/auth/login";
        readonly REGISTER: "/auth/register";
        readonly LOGOUT: "/auth/logout";
        readonly REFRESH: "/auth/refresh";
    };
    readonly USERS: {
        readonly PROFILE: "/users/profile";
        readonly UPDATE: "/users/update";
    };
    readonly PRODUCTS: {
        readonly LIST: "/products";
        readonly DETAIL: "/products/:id";
        readonly CREATE: "/products";
        readonly UPDATE: "/products/:id";
        readonly DELETE: "/products/:id";
    };
    readonly ORDERS: {
        readonly LIST: "/orders";
        readonly DETAIL: "/orders/:id";
        readonly CREATE: "/orders";
        readonly UPDATE: "/orders/:id";
    };
};
//# sourceMappingURL=index.d.ts.map