
> @zenera/ui-components@1.0.0 build /Volumes/Work/DuAnNgoai/Zenera/Zenera/packages/ui-components
> tsc

[96msrc/components/primitives/Input.tsx[0m:[93m36[0m:[93m18[0m - [91merror[0m[90m TS2320: [0mInterface 'InputProps' cannot simultaneously extend types 'InputHTMLAttributes<HTMLInputElement>' and 'VariantProps<(props?: (ConfigVariants<{ variant: { default: string; error: string; success: string; }; size: { default: string; sm: string; lg: string; xl: string; }; animation: { none: string; focus: string; glow: string; lift: string; }; }> & ClassProp) | undefined) => string>'.
  Named property 'size' of types 'InputHTMLAttributes<HTMLInputElement>' and 'VariantProps<(props?: (ConfigVariants<{ variant: { default: string; error: string; success: string; }; size: { default: string; sm: string; lg: string; xl: string; }; animation: { none: string; focus: string; glow: string; lift: string; }; }> & ClassProp) | undefined) => string>' are not identical.

[7m36[0m export interface InputProps
[7m  [0m [91m                 ~~~~~~~~~~[0m

[96msrc/components/primitives/Input.tsx[0m:[93m81[0m:[93m57[0m - [91merror[0m[90m TS2322: [0mType 'number | undefined' is not assignable to type '"default" | "sm" | "lg" | "xl" | null | undefined'.
  Type 'number' is not assignable to type '"default" | "sm" | "lg" | "xl" | null | undefined'.

[7m81[0m               inputVariants({ variant: computedVariant, size, animation }),
[7m  [0m [91m                                                        ~~~~[0m

[96msrc/tokens/colors.ts[0m:[93m115[0m:[93m10[0m - [91merror[0m[90m TS7053: [0mElement implicitly has an 'any' type because expression of type '500 | 50 | 100 | 200 | 300 | 400 | 600 | 700 | 800 | 900 | 950' can't be used to index type '{ readonly 50: "#eff6ff"; readonly 100: "#dbeafe"; readonly 200: "#bfdbfe"; readonly 300: "#93c5fd"; readonly 400: "#60a5fa"; readonly 500: "#3b82f6"; readonly 600: "#2563eb"; readonly 700: "#1d4ed8"; readonly 800: "#1e40af"; readonly 900: "#1e3a8a"; readonly 950: "#172554"; } | ... 7 more ... | { ...; }'.
  Property '500' does not exist on type '{ readonly 50: "#eff6ff"; readonly 100: "#dbeafe"; readonly 200: "#bfdbfe"; readonly 300: "#93c5fd"; readonly 400: "#60a5fa"; readonly 500: "#3b82f6"; readonly 600: "#2563eb"; readonly 700: "#1d4ed8"; readonly 800: "#1e40af"; readonly 900: "#1e3a8a"; readonly 950: "#172554"; } | ... 7 more ... | { ...; }'.

[7m115[0m   return colors[colorName as keyof typeof colors]?.[shadeValue as keyof typeof colors.primary] || color;
[7m   [0m [91m         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m


Found 3 errors in 2 files.

Errors  Files
     2  src/components/primitives/Input.tsx[90m:36[0m
     1  src/tokens/colors.ts[90m:115[0m
[41m[30m ELIFECYCLE [39m[49m [31mCommand failed with exit code 2.[39m
