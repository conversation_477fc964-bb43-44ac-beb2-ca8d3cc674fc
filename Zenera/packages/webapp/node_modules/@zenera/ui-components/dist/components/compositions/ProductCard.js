import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import * as React from 'react';
import { cva } from 'class-variance-authority';
import { cn } from '../../utils';
import { Card, CardContent } from '../primitives/Card';
import { Button } from '../primitives/Button';
const productCardVariants = cva('group relative overflow-hidden transition-all duration-300', {
    variants: {
        variant: {
            default: 'hover:-translate-y-2 hover:shadow-xl',
            compact: 'hover:-translate-y-1 hover:shadow-lg',
            featured: 'hover:-translate-y-3 hover:shadow-2xl hover:shadow-primary-500/20',
            minimal: 'hover:shadow-md',
        },
        size: {
            sm: 'max-w-xs',
            default: 'max-w-sm',
            lg: 'max-w-md',
            xl: 'max-w-lg',
        },
    },
    defaultVariants: {
        variant: 'default',
        size: 'default',
    },
});
const ProductCard = React.forwardRef(({ className, variant, size, product, onAddToCart, onAddToWishlist, onQuickView, showQuickActions = true, loading = false, ...props }, ref) => {
    const discountPercentage = product.originalPrice
        ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
        : 0;
    return (_jsxs(Card, { ref: ref, className: cn(productCardVariants({ variant, size }), className), padding: "none", interactive: true, ...props, children: [_jsxs("div", { className: "relative aspect-square overflow-hidden bg-neutral-100", children: [_jsx("img", { src: product.image, alt: product.name, className: "h-full w-full object-cover transition-transform duration-500 group-hover:scale-110", loading: "lazy" }), _jsxs("div", { className: "absolute top-2 left-2 flex flex-col gap-1", children: [product.badge && (_jsx("span", { className: "rounded-full bg-primary-500 px-2 py-1 text-xs font-medium text-white", children: product.badge })), discountPercentage > 0 && (_jsxs("span", { className: "rounded-full bg-error-500 px-2 py-1 text-xs font-medium text-white animate-pulse", children: ["-", discountPercentage, "%"] })), !product.inStock && (_jsx("span", { className: "rounded-full bg-neutral-500 px-2 py-1 text-xs font-medium text-white", children: "Out of Stock" }))] }), showQuickActions && (_jsxs("div", { className: "absolute top-2 right-2 flex flex-col gap-1 opacity-0 transition-opacity duration-200 group-hover:opacity-100", children: [onAddToWishlist && (_jsx(Button, { size: "icon", variant: "ghost", className: "h-8 w-8 bg-white/80 backdrop-blur-sm hover:bg-white", onClick: (e) => {
                                    e.stopPropagation();
                                    onAddToWishlist(product.id);
                                }, children: _jsx("svg", { className: "h-4 w-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: _jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" }) }) })), onQuickView && (_jsx(Button, { size: "icon", variant: "ghost", className: "h-8 w-8 bg-white/80 backdrop-blur-sm hover:bg-white", onClick: (e) => {
                                    e.stopPropagation();
                                    onQuickView(product.id);
                                }, children: _jsxs("svg", { className: "h-4 w-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: [_jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M15 12a3 3 0 11-6 0 3 3 0 016 0z" }), _jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" })] }) }))] }))] }), _jsxs(CardContent, { className: "p-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx("h3", { className: "font-medium text-neutral-900 line-clamp-2 group-hover:text-primary-600 transition-colors", children: product.name }), product.rating && (_jsxs("div", { className: "flex items-center gap-1", children: [_jsx("div", { className: "flex", children: [...Array(5)].map((_, i) => (_jsx("svg", { className: cn('h-4 w-4', i < Math.floor(product.rating)
                                                ? 'text-warning-400 fill-current'
                                                : 'text-neutral-300'), fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: _jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" }) }, i))) }), product.reviewCount && (_jsxs("span", { className: "text-sm text-neutral-500", children: ["(", product.reviewCount, ")"] }))] })), _jsxs("div", { className: "flex items-center gap-2", children: [_jsxs("span", { className: "text-lg font-bold text-neutral-900", children: ["$", product.price.toFixed(2)] }), product.originalPrice && (_jsxs("span", { className: "text-sm text-neutral-500 line-through", children: ["$", product.originalPrice.toFixed(2)] }))] })] }), onAddToCart && product.inStock !== false && (_jsx(Button, { variant: "cart", className: "w-full mt-3", onClick: (e) => {
                            e.stopPropagation();
                            onAddToCart(product.id);
                        }, loading: loading, disabled: !product.inStock, children: "Add to Cart" }))] })] }));
});
ProductCard.displayName = 'ProductCard';
export { ProductCard, productCardVariants };
