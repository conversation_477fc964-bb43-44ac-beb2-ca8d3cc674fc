import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import * as React from 'react';
import { cva } from 'class-variance-authority';
import { cn } from '../../utils';
const buttonVariants = cva(
// Base styles với Zenera animations
'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-[0.98] hover:-translate-y-0.5 hover:shadow-lg', {
    variants: {
        variant: {
            default: 'bg-primary-500 text-white hover:bg-primary-600 hover:shadow-primary-500/25 active:bg-primary-700',
            destructive: 'bg-error-500 text-white hover:bg-error-600 hover:shadow-error-500/25 active:bg-error-700',
            outline: 'border border-neutral-200 bg-white hover:bg-neutral-50 hover:text-neutral-900 hover:border-neutral-300',
            secondary: 'bg-secondary-100 text-secondary-900 hover:bg-secondary-200 hover:shadow-secondary-500/10',
            ghost: 'hover:bg-neutral-100 hover:text-neutral-900',
            link: 'text-primary-500 underline-offset-4 hover:underline hover:text-primary-600',
            // E-commerce specific variants
            cart: 'bg-success-500 text-white hover:bg-success-600 hover:shadow-success-500/25 active:bg-success-700 hover:scale-105',
            buy: 'bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:from-primary-600 hover:to-primary-700 hover:shadow-xl hover:shadow-primary-500/30',
            wishlist: 'border border-neutral-200 bg-white text-neutral-700 hover:bg-neutral-50 hover:text-error-500 hover:border-error-200',
        },
        size: {
            default: 'h-10 px-4 py-2',
            sm: 'h-9 rounded-md px-3',
            lg: 'h-11 rounded-md px-8',
            xl: 'h-12 rounded-lg px-10 text-base',
            icon: 'h-10 w-10',
        },
        animation: {
            none: '',
            bounce: 'hover:animate-bounce',
            pulse: 'hover:animate-pulse',
            glow: 'hover:shadow-lg hover:shadow-primary-500/25',
            lift: 'hover:-translate-y-1 transition-transform duration-200',
        },
    },
    defaultVariants: {
        variant: 'default',
        size: 'default',
        animation: 'lift',
    },
});
const Button = React.forwardRef(({ className, variant, size, animation, loading = false, leftIcon, rightIcon, children, disabled, asChild = false, ...props }, ref) => {
    const isDisabled = disabled || loading;
    return (_jsxs("button", { className: cn(buttonVariants({ variant, size, animation, className }), loading && 'cursor-not-allowed opacity-70'), ref: ref, disabled: isDisabled, ...props, children: [loading && (_jsxs("svg", { className: "mr-2 h-4 w-4 animate-spin", xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 24 24", children: [_jsx("circle", { className: "opacity-25", cx: "12", cy: "12", r: "10", stroke: "currentColor", strokeWidth: "4" }), _jsx("path", { className: "opacity-75", fill: "currentColor", d: "m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" })] })), !loading && leftIcon && (_jsx("span", { className: "mr-2", children: leftIcon })), children, !loading && rightIcon && (_jsx("span", { className: "ml-2", children: rightIcon }))] }));
});
Button.displayName = 'Button';
export { Button, buttonVariants };
