export declare const typography: {
    readonly fontFamily: {
        readonly sans: readonly ["Inter", "system-ui", "sans-serif"];
        readonly serif: readonly ["Georgia", "serif"];
        readonly mono: readonly ["JetBrains Mono", "Consolas", "monospace"];
        readonly display: readonly ["Poppins", "Inter", "system-ui", "sans-serif"];
    };
    readonly fontSize: {
        readonly xs: readonly ["0.75rem", {
            readonly lineHeight: "1rem";
        }];
        readonly sm: readonly ["0.875rem", {
            readonly lineHeight: "1.25rem";
        }];
        readonly base: readonly ["1rem", {
            readonly lineHeight: "1.5rem";
        }];
        readonly lg: readonly ["1.125rem", {
            readonly lineHeight: "1.75rem";
        }];
        readonly xl: readonly ["1.25rem", {
            readonly lineHeight: "1.75rem";
        }];
        readonly '2xl': readonly ["1.5rem", {
            readonly lineHeight: "2rem";
        }];
        readonly '3xl': readonly ["1.875rem", {
            readonly lineHeight: "2.25rem";
        }];
        readonly '4xl': readonly ["2.25rem", {
            readonly lineHeight: "2.5rem";
        }];
        readonly '5xl': readonly ["3rem", {
            readonly lineHeight: "1";
        }];
        readonly '6xl': readonly ["3.75rem", {
            readonly lineHeight: "1";
        }];
        readonly '7xl': readonly ["4.5rem", {
            readonly lineHeight: "1";
        }];
        readonly '8xl': readonly ["6rem", {
            readonly lineHeight: "1";
        }];
        readonly '9xl': readonly ["8rem", {
            readonly lineHeight: "1";
        }];
    };
    readonly fontWeight: {
        readonly thin: "100";
        readonly extralight: "200";
        readonly light: "300";
        readonly normal: "400";
        readonly medium: "500";
        readonly semibold: "600";
        readonly bold: "700";
        readonly extrabold: "800";
        readonly black: "900";
    };
    readonly lineHeight: {
        readonly none: "1";
        readonly tight: "1.25";
        readonly snug: "1.375";
        readonly normal: "1.5";
        readonly relaxed: "1.625";
        readonly loose: "2";
    };
    readonly letterSpacing: {
        readonly tighter: "-0.05em";
        readonly tight: "-0.025em";
        readonly normal: "0em";
        readonly wide: "0.025em";
        readonly wider: "0.05em";
        readonly widest: "0.1em";
    };
    readonly textStyles: {
        readonly 'heading-1': {
            readonly fontFamily: "Poppins";
            readonly fontSize: "3rem";
            readonly fontWeight: "700";
            readonly lineHeight: "1.2";
            readonly letterSpacing: "-0.025em";
        };
        readonly 'heading-2': {
            readonly fontFamily: "Poppins";
            readonly fontSize: "2.25rem";
            readonly fontWeight: "600";
            readonly lineHeight: "1.3";
            readonly letterSpacing: "-0.025em";
        };
        readonly 'heading-3': {
            readonly fontFamily: "Poppins";
            readonly fontSize: "1.875rem";
            readonly fontWeight: "600";
            readonly lineHeight: "1.4";
        };
        readonly 'heading-4': {
            readonly fontFamily: "Poppins";
            readonly fontSize: "1.5rem";
            readonly fontWeight: "600";
            readonly lineHeight: "1.4";
        };
        readonly 'heading-5': {
            readonly fontFamily: "Poppins";
            readonly fontSize: "1.25rem";
            readonly fontWeight: "600";
            readonly lineHeight: "1.5";
        };
        readonly 'heading-6': {
            readonly fontFamily: "Poppins";
            readonly fontSize: "1.125rem";
            readonly fontWeight: "600";
            readonly lineHeight: "1.5";
        };
        readonly 'body-large': {
            readonly fontFamily: "Inter";
            readonly fontSize: "1.125rem";
            readonly fontWeight: "400";
            readonly lineHeight: "1.7";
        };
        readonly 'body-base': {
            readonly fontFamily: "Inter";
            readonly fontSize: "1rem";
            readonly fontWeight: "400";
            readonly lineHeight: "1.6";
        };
        readonly 'body-small': {
            readonly fontFamily: "Inter";
            readonly fontSize: "0.875rem";
            readonly fontWeight: "400";
            readonly lineHeight: "1.5";
        };
        readonly 'product-title': {
            readonly fontFamily: "Poppins";
            readonly fontSize: "1.5rem";
            readonly fontWeight: "600";
            readonly lineHeight: "1.4";
        };
        readonly 'product-price': {
            readonly fontFamily: "Poppins";
            readonly fontSize: "1.25rem";
            readonly fontWeight: "700";
            readonly lineHeight: "1.2";
        };
        readonly 'product-description': {
            readonly fontFamily: "Inter";
            readonly fontSize: "0.875rem";
            readonly fontWeight: "400";
            readonly lineHeight: "1.6";
        };
        readonly 'button-text': {
            readonly fontFamily: "Inter";
            readonly fontSize: "0.875rem";
            readonly fontWeight: "600";
            readonly lineHeight: "1.2";
            readonly letterSpacing: "0.025em";
        };
        readonly label: {
            readonly fontFamily: "Inter";
            readonly fontSize: "0.875rem";
            readonly fontWeight: "500";
            readonly lineHeight: "1.4";
        };
        readonly caption: {
            readonly fontFamily: "Inter";
            readonly fontSize: "0.75rem";
            readonly fontWeight: "400";
            readonly lineHeight: "1.4";
        };
    };
};
export type FontFamily = keyof typeof typography.fontFamily;
export type FontSize = keyof typeof typography.fontSize;
export type FontWeight = keyof typeof typography.fontWeight;
export type TextStyle = keyof typeof typography.textStyles;
//# sourceMappingURL=typography.d.ts.map