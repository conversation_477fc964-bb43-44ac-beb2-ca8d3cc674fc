export declare const animations: {
    readonly duration: {
        readonly instant: "0ms";
        readonly fast: "150ms";
        readonly normal: "300ms";
        readonly slow: "500ms";
        readonly slower: "750ms";
        readonly slowest: "1000ms";
    };
    readonly easing: {
        readonly linear: "linear";
        readonly ease: "ease";
        readonly easeIn: "ease-in";
        readonly easeOut: "ease-out";
        readonly easeInOut: "ease-in-out";
        readonly smooth: "cubic-bezier(0.4, 0, 0.2, 1)";
        readonly bounce: "cubic-bezier(0.68, -0.55, 0.265, 1.55)";
        readonly elastic: "cubic-bezier(0.175, 0.885, 0.32, 1.275)";
    };
    readonly keyframes: {
        readonly fadeIn: {
            readonly from: {
                readonly opacity: "0";
            };
            readonly to: {
                readonly opacity: "1";
            };
        };
        readonly fadeOut: {
            readonly from: {
                readonly opacity: "1";
            };
            readonly to: {
                readonly opacity: "0";
            };
        };
        readonly slideInUp: {
            readonly from: {
                readonly transform: "translateY(100%)";
                readonly opacity: "0";
            };
            readonly to: {
                readonly transform: "translateY(0)";
                readonly opacity: "1";
            };
        };
        readonly slideInDown: {
            readonly from: {
                readonly transform: "translateY(-100%)";
                readonly opacity: "0";
            };
            readonly to: {
                readonly transform: "translateY(0)";
                readonly opacity: "1";
            };
        };
        readonly slideInLeft: {
            readonly from: {
                readonly transform: "translateX(-100%)";
                readonly opacity: "0";
            };
            readonly to: {
                readonly transform: "translateX(0)";
                readonly opacity: "1";
            };
        };
        readonly slideInRight: {
            readonly from: {
                readonly transform: "translateX(100%)";
                readonly opacity: "0";
            };
            readonly to: {
                readonly transform: "translateX(0)";
                readonly opacity: "1";
            };
        };
        readonly scaleIn: {
            readonly from: {
                readonly transform: "scale(0.9)";
                readonly opacity: "0";
            };
            readonly to: {
                readonly transform: "scale(1)";
                readonly opacity: "1";
            };
        };
        readonly scaleOut: {
            readonly from: {
                readonly transform: "scale(1)";
                readonly opacity: "1";
            };
            readonly to: {
                readonly transform: "scale(0.9)";
                readonly opacity: "0";
            };
        };
        readonly bounce: {
            readonly '0%, 20%, 53%, 80%, 100%': {
                readonly transform: "translate3d(0,0,0)";
            };
            readonly '40%, 43%': {
                readonly transform: "translate3d(0, -30px, 0)";
            };
            readonly '70%': {
                readonly transform: "translate3d(0, -15px, 0)";
            };
            readonly '90%': {
                readonly transform: "translate3d(0, -4px, 0)";
            };
        };
        readonly pulse: {
            readonly '0%, 100%': {
                readonly opacity: "1";
            };
            readonly '50%': {
                readonly opacity: "0.5";
            };
        };
        readonly spin: {
            readonly from: {
                readonly transform: "rotate(0deg)";
            };
            readonly to: {
                readonly transform: "rotate(360deg)";
            };
        };
        readonly wiggle: {
            readonly '0%, 100%': {
                readonly transform: "rotate(-3deg)";
            };
            readonly '50%': {
                readonly transform: "rotate(3deg)";
            };
        };
        readonly addToCart: {
            readonly '0%': {
                readonly transform: "scale(1)";
            };
            readonly '50%': {
                readonly transform: "scale(1.1)";
            };
            readonly '100%': {
                readonly transform: "scale(1)";
            };
        };
        readonly priceChange: {
            readonly '0%': {
                readonly transform: "scale(1)";
                readonly color: "inherit";
            };
            readonly '50%': {
                readonly transform: "scale(1.05)";
                readonly color: "#22c55e";
            };
            readonly '100%': {
                readonly transform: "scale(1)";
                readonly color: "inherit";
            };
        };
        readonly stockAlert: {
            readonly '0%, 100%': {
                readonly backgroundColor: "transparent";
            };
            readonly '50%': {
                readonly backgroundColor: "#fef3c7";
            };
        };
    };
    readonly presets: {
        readonly fadeIn: {
            readonly animation: "fadeIn 300ms cubic-bezier(0.4, 0, 0.2, 1)";
        };
        readonly slideInUp: {
            readonly animation: "slideInUp 300ms cubic-bezier(0.4, 0, 0.2, 1)";
        };
        readonly slideInDown: {
            readonly animation: "slideInDown 300ms cubic-bezier(0.4, 0, 0.2, 1)";
        };
        readonly scaleIn: {
            readonly animation: "scaleIn 200ms cubic-bezier(0.4, 0, 0.2, 1)";
        };
        readonly hoverLift: {
            readonly transition: "transform 150ms cubic-bezier(0.4, 0, 0.2, 1)";
            readonly '&:hover': {
                readonly transform: "translateY(-2px)";
            };
        };
        readonly hoverScale: {
            readonly transition: "transform 150ms cubic-bezier(0.4, 0, 0.2, 1)";
            readonly '&:hover': {
                readonly transform: "scale(1.02)";
            };
        };
        readonly hoverGlow: {
            readonly transition: "box-shadow 150ms cubic-bezier(0.4, 0, 0.2, 1)";
            readonly '&:hover': {
                readonly boxShadow: "0 10px 25px -5px rgba(59, 130, 246, 0.15)";
            };
        };
        readonly pulse: {
            readonly animation: "pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite";
        };
        readonly spin: {
            readonly animation: "spin 1s linear infinite";
        };
        readonly addToCart: {
            readonly animation: "addToCart 200ms cubic-bezier(0.68, -0.55, 0.265, 1.55)";
        };
        readonly priceChange: {
            readonly animation: "priceChange 500ms cubic-bezier(0.4, 0, 0.2, 1)";
        };
        readonly stockAlert: {
            readonly animation: "stockAlert 1s ease-in-out infinite";
        };
        readonly buttonPress: {
            readonly transition: "transform 100ms cubic-bezier(0.4, 0, 0.2, 1)";
            readonly '&:active': {
                readonly transform: "scale(0.98)";
            };
        };
        readonly pageEnter: {
            readonly animation: "fadeIn 400ms cubic-bezier(0.4, 0, 0.2, 1)";
        };
        readonly pageExit: {
            readonly animation: "fadeOut 200ms cubic-bezier(0.4, 0, 0.2, 1)";
        };
    };
    readonly microInteractions: {
        readonly inputFocus: {
            readonly transition: "all 150ms cubic-bezier(0.4, 0, 0.2, 1)";
            readonly '&:focus': {
                readonly transform: "scale(1.01)";
                readonly boxShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)";
            };
        };
        readonly cardHover: {
            readonly transition: "all 200ms cubic-bezier(0.4, 0, 0.2, 1)";
            readonly '&:hover': {
                readonly transform: "translateY(-4px)";
                readonly boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1)";
            };
        };
        readonly navItemHover: {
            readonly transition: "color 150ms cubic-bezier(0.4, 0, 0.2, 1)";
            readonly position: "relative";
            readonly '&::after': {
                readonly content: "\"\"";
                readonly position: "absolute";
                readonly bottom: "-2px";
                readonly left: "0";
                readonly width: "0";
                readonly height: "2px";
                readonly backgroundColor: "currentColor";
                readonly transition: "width 200ms cubic-bezier(0.4, 0, 0.2, 1)";
            };
            readonly '&:hover::after': {
                readonly width: "100%";
            };
        };
    };
};
export type AnimationDuration = keyof typeof animations.duration;
export type AnimationEasing = keyof typeof animations.easing;
export type AnimationPreset = keyof typeof animations.presets;
//# sourceMappingURL=animations.d.ts.map