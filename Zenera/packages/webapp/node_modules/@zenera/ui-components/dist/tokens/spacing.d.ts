export declare const spacing: {
    readonly 0: "0";
    readonly px: "1px";
    readonly 0.5: "0.125rem";
    readonly 1: "0.25rem";
    readonly 1.5: "0.375rem";
    readonly 2: "0.5rem";
    readonly 2.5: "0.625rem";
    readonly 3: "0.75rem";
    readonly 3.5: "0.875rem";
    readonly 4: "1rem";
    readonly 5: "1.25rem";
    readonly 6: "1.5rem";
    readonly 7: "1.75rem";
    readonly 8: "2rem";
    readonly 9: "2.25rem";
    readonly 10: "2.5rem";
    readonly 11: "2.75rem";
    readonly 12: "3rem";
    readonly 14: "3.5rem";
    readonly 16: "4rem";
    readonly 20: "5rem";
    readonly 24: "6rem";
    readonly 28: "7rem";
    readonly 32: "8rem";
    readonly 36: "9rem";
    readonly 40: "10rem";
    readonly 44: "11rem";
    readonly 48: "12rem";
    readonly 52: "13rem";
    readonly 56: "14rem";
    readonly 60: "15rem";
    readonly 64: "16rem";
    readonly 72: "18rem";
    readonly 80: "20rem";
    readonly 96: "24rem";
};
export declare const componentSpacing: {
    readonly button: {
        readonly padding: {
            readonly sm: {
                readonly x: "0.75rem";
                readonly y: "0.375rem";
            };
            readonly md: {
                readonly x: "1rem";
                readonly y: "0.5rem";
            };
            readonly lg: {
                readonly x: "1.5rem";
                readonly y: "0.75rem";
            };
            readonly xl: {
                readonly x: "2rem";
                readonly y: "1rem";
            };
        };
        readonly gap: "0.5rem";
    };
    readonly input: {
        readonly padding: {
            readonly sm: {
                readonly x: "0.75rem";
                readonly y: "0.375rem";
            };
            readonly md: {
                readonly x: "1rem";
                readonly y: "0.625rem";
            };
            readonly lg: {
                readonly x: "1rem";
                readonly y: "0.75rem";
            };
        };
    };
    readonly card: {
        readonly padding: {
            readonly sm: "1rem";
            readonly md: "1.5rem";
            readonly lg: "2rem";
        };
        readonly gap: "1rem";
    };
    readonly layout: {
        readonly container: {
            readonly padding: {
                readonly x: "1rem";
                readonly y: "2rem";
            };
            readonly maxWidth: "1200px";
        };
        readonly section: {
            readonly padding: {
                readonly y: "4rem";
            };
            readonly gap: "3rem";
        };
        readonly grid: {
            readonly gap: "1.5rem";
        };
    };
    readonly product: {
        readonly card: {
            readonly padding: "1rem";
            readonly gap: "0.75rem";
            readonly imageAspect: "1/1";
        };
        readonly gallery: {
            readonly gap: "0.5rem";
            readonly thumbnailSize: "4rem";
        };
        readonly details: {
            readonly gap: "1.5rem";
            readonly sectionGap: "2rem";
        };
    };
    readonly navigation: {
        readonly header: {
            readonly height: "4rem";
            readonly padding: {
                readonly x: "1.5rem";
            };
        };
        readonly sidebar: {
            readonly width: "16rem";
            readonly padding: "1.5rem";
        };
        readonly breadcrumb: {
            readonly gap: "0.5rem";
            readonly padding: {
                readonly y: "1rem";
            };
        };
    };
    readonly form: {
        readonly fieldGap: "1rem";
        readonly sectionGap: "2rem";
        readonly labelGap: "0.375rem";
        readonly buttonGap: "1rem";
    };
};
export declare const borderRadius: {
    readonly none: "0";
    readonly sm: "0.125rem";
    readonly base: "0.25rem";
    readonly md: "0.375rem";
    readonly lg: "0.5rem";
    readonly xl: "0.75rem";
    readonly '2xl': "1rem";
    readonly '3xl': "1.5rem";
    readonly full: "9999px";
};
export declare const shadows: {
    readonly sm: "0 1px 2px 0 rgb(0 0 0 / 0.05)";
    readonly base: "0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)";
    readonly md: "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)";
    readonly lg: "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)";
    readonly xl: "0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)";
    readonly '2xl': "0 25px 50px -12px rgb(0 0 0 / 0.25)";
    readonly inner: "inset 0 2px 4px 0 rgb(0 0 0 / 0.05)";
    readonly none: "0 0 #0000";
};
export type Spacing = keyof typeof spacing;
export type BorderRadius = keyof typeof borderRadius;
export type Shadow = keyof typeof shadows;
//# sourceMappingURL=spacing.d.ts.map