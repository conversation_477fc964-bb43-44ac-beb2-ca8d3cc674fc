module.exports = {

"[project]/packages/webapp/postcss.config.mjs/transform.ts { CONFIG => \"[project]/packages/webapp/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "build/chunks/node_modules__pnpm_ec38a98b._.js",
  "build/chunks/[root-of-the-server]__4b5293e8._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/packages/webapp/postcss.config.mjs/transform.ts { CONFIG => \"[project]/packages/webapp/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript)");
    });
});
}}),

};