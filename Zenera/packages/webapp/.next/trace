[{"name": "hot-reloader", "duration": 191, "timestamp": 6320688660, "id": 3, "tags": {"version": "15.3.3"}, "startTime": 1748850495265, "traceId": "1826c10f2916080f"}, {"name": "setup-dev-bundler", "duration": 820379, "timestamp": 6320325089, "id": 2, "parentId": 1, "tags": {}, "startTime": 1748850494901, "traceId": "1826c10f2916080f"}, {"name": "run-instrumentation-hook", "duration": 40, "timestamp": 6321226639, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748850495803, "traceId": "1826c10f2916080f"}, {"name": "start-dev-server", "duration": 1660783, "timestamp": 6319583464, "id": 1, "tags": {"cpus": "12", "platform": "darwin", "memory.freeMem": "7373869056", "memory.totalMem": "34359738368", "memory.heapSizeLimit": "17230200832", "memory.rss": "233320448", "memory.heapTotal": "99405824", "memory.heapUsed": "73268592"}, "startTime": 1748850494160, "traceId": "1826c10f2916080f"}, {"name": "compile-path", "duration": 2840190, "timestamp": 6342697522, "id": 7, "tags": {"trigger": "/"}, "startTime": 1748850517274, "traceId": "1826c10f2916080f"}, {"name": "ensure-page", "duration": 2841353, "timestamp": 6342696924, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1748850517273, "traceId": "1826c10f2916080f"}]