[{"/Volumes/Work/DuAnNgoai/Zenera/Zenera/packages/webapp/src/app/design-system-demo.tsx": "1", "/Volumes/Work/DuAnNgoai/Zenera/Zenera/packages/webapp/src/app/layout.tsx": "2", "/Volumes/Work/DuAnNgoai/Zenera/Zenera/packages/webapp/src/app/page.tsx": "3"}, {"size": 7492, "mtime": 1748851603035, "results": "4", "hashOfConfig": "5"}, {"size": 689, "mtime": 1748850277189, "results": "6", "hashOfConfig": "5"}, {"size": 4086, "mtime": 1748850277195, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sh5olr", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Volumes/Work/DuAnNgoai/Zenera/Zenera/packages/webapp/src/app/design-system-demo.tsx", [], [], "/Volumes/Work/DuAnNgoai/Zenera/Zenera/packages/webapp/src/app/layout.tsx", [], [], "/Volumes/Work/DuAnNgoai/Zenera/Zenera/packages/webapp/src/app/page.tsx", [], []]